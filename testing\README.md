# ATMA E2E Testing Suite

Comprehensive End-to-End testing client for ATMA Backend Services.

## Overview

This testing suite provides automated E2E testing for the complete ATMA user flow:

1. **User Registration** - Register with random email
2. **User Login** - Authenticate and get JWT token
3. **WebSocket Connection** - Connect and authenticate via WebSocket
4. **Profile Update** - Update user profile information
5. **Assessment Submission** - Submit assessment data for AI analysis
6. **WebSocket Notifications** - Wait for real-time notifications
7. **Profile Persona** - Retrieve generated persona profile
8. **Chatbot Testing** - Test AI chatbot functionality
9. **Account Deletion** - Clean up test accounts

## Features

- **Dual User Testing**: Test with 2 users simultaneously
- **Random Email Generation**: Each test run uses unique emails
- **WebSocket Real-time Testing**: Full WebSocket connection and notification testing
- **Comprehensive API Coverage**: Tests all major API endpoints
- **Response Validation**: Validates request/response bodies and headers
- **Error Handling**: Robust error handling and cleanup
- **Detailed Logging**: Comprehensive logging with timestamps
- **Configurable Timeouts**: Adjustable timeouts for different operations

## Installation

```bash
cd testing
npm install
```

## Configuration

Edit `.env` file to configure testing parameters:

```env
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000
TEST_TIMEOUT=30000
ASSESSMENT_TIMEOUT=300000
```

## Usage

### Run Complete E2E Test Suite
```bash
npm test
```

### Run Single User Test
```bash
npm run test:single
```

### Run Dual User Test
```bash
npm run test:dual
```

### Run WebSocket Only Test
```bash
npm run test:websocket
```

### Run Chatbot Only Test
```bash
npm run test:chatbot
```

### Cleanup Test Users
```bash
npm run test:cleanup
```

## Test Flow

### Complete E2E Flow
1. Generate random test users
2. Register both users
3. Login both users
4. Connect WebSocket for both users
5. Update profiles for both users
6. Submit assessments for both users
7. Wait for WebSocket notifications
8. Retrieve persona profiles
9. Test chatbot functionality
10. Delete test accounts

### Validation Points
- HTTP status codes
- Response body structure
- Required headers
- JWT token format
- WebSocket event format
- Assessment data validation
- Persona profile completeness

## File Structure

```
testing/
├── package.json              # Dependencies and scripts
├── .env                     # Configuration
├── README.md                # This file
├── test-runner.js           # Main test runner
├── single-user-test.js      # Single user test
├── dual-user-test.js        # Dual user test
├── lib/
│   ├── api-client.js        # HTTP API client
│   ├── websocket-client.js  # WebSocket client
│   ├── test-data.js         # Test data generators
│   ├── validators.js        # Response validators
│   └── logger.js            # Logging utilities
├── tests/
│   ├── auth-test.js         # Authentication tests
│   ├── profile-test.js      # Profile management tests
│   ├── assessment-test.js   # Assessment submission tests
│   ├── websocket-test.js    # WebSocket notification tests
│   ├── chatbot-test.js      # Chatbot functionality tests
│   └── cleanup-test.js      # Cleanup utilities
└── results/                 # Test results and logs
    ├── logs/               # Test execution logs
    └── responses/          # Saved API responses
```

## Requirements

- Node.js 18+
- ATMA Backend Services running
- API Gateway accessible at localhost:3000
- WebSocket service accessible at localhost:3000

## Examples

### Basic Usage Examples

```bash
# Install dependencies
cd testing
npm install

# Run complete E2E test with 2 users
npm test

# Run single user test
npm run test:single

# Run dual user test with parallel execution
npm run test:dual

# Test only WebSocket functionality
npm run test:websocket

# Test only chatbot functionality
npm run test:chatbot

# Clean up test users and old files
npm run test:cleanup
```

### Advanced Usage

```bash
# Run with custom timeout
ASSESSMENT_TIMEOUT=600000 npm test

# Run with debug logging
DEBUG_MODE=true LOG_LEVEL=debug npm test

# Run without saving responses
SAVE_RESPONSES=false npm test

# Run with custom API URL
API_BASE_URL=http://localhost:4000/api npm test
```

### Environment Variables

```env
# Required
API_BASE_URL=http://localhost:3000/api
WEBSOCKET_URL=http://localhost:3000

# Optional
TEST_TIMEOUT=30000                    # HTTP request timeout
ASSESSMENT_TIMEOUT=300000             # Assessment completion timeout
WEBSOCKET_TIMEOUT=10000               # WebSocket auth timeout
TEST_EMAIL_DOMAIN=test.atma.local     # Test email domain
TEST_PASSWORD=TestPassword123!        # Test user password
DEBUG_MODE=true                       # Enable debug logging
LOG_LEVEL=info                        # Log level (error, warn, info, debug)
SAVE_RESPONSES=true                   # Save API responses to files
DEFAULT_ASSESSMENT_NAME=E2E Test      # Default assessment name
```

## Test Scenarios

### Scenario 1: Complete E2E Flow
Tests the entire user journey from registration to account deletion:
- User registration with random email
- User login and JWT token validation
- WebSocket connection and authentication
- Profile update with validation
- Assessment submission and processing
- Real-time notification handling
- Persona profile retrieval and validation
- Chatbot conversation testing
- Resource cleanup and account deletion

### Scenario 2: Parallel User Testing
Tests system behavior with multiple concurrent users:
- Simultaneous user registration
- Parallel assessment submissions
- Concurrent WebSocket connections
- Real-time notification handling for multiple users
- Resource contention testing

### Scenario 3: WebSocket Stress Testing
Focuses on WebSocket functionality:
- Connection establishment and authentication
- Notification delivery and validation
- Reconnection handling
- Connection state management
- Error handling and recovery

### Scenario 4: Chatbot Integration Testing
Tests AI chatbot functionality:
- Conversation creation and management
- Message sending and response validation
- Assessment-based conversation initialization
- Context preservation and conversation history
- Error handling and edge cases

## Troubleshooting

### Common Issues

1. **Connection Refused**: Ensure all ATMA services are running
   ```bash
   # Check if services are running
   curl http://localhost:3000/api/health
   ```

2. **Authentication Timeout**: Check JWT token validity
   ```bash
   # Verify token format in logs
   grep "JWT" testing/results/logs/*.log
   ```

3. **WebSocket Connection Failed**: Verify WebSocket service is running
   ```bash
   # Check WebSocket health
   curl http://localhost:3000/api/notifications/health
   ```

4. **Assessment Timeout**: Increase ASSESSMENT_TIMEOUT in .env
   ```env
   ASSESSMENT_TIMEOUT=600000  # 10 minutes
   ```

5. **Rate Limiting**: Reduce concurrent requests or increase rate limits
   ```env
   # Add delays between requests
   TEST_DELAY=1000
   ```

### Debug Mode

Enable debug mode in `.env`:
```env
DEBUG_MODE=true
LOG_LEVEL=debug
SAVE_RESPONSES=true
```

This will provide:
- Detailed logging of all requests and responses
- WebSocket event logging
- Saved API responses in `results/responses/`
- Comprehensive test execution logs

### Log Analysis

```bash
# View latest test log
tail -f testing/results/logs/test-*.log

# Search for errors
grep -i error testing/results/logs/*.log

# Find specific user actions
grep "<EMAIL>" testing/results/logs/*.log

# Check WebSocket events
grep "WebSocket" testing/results/logs/*.log
```
