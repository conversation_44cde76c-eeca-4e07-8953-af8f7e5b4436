const axios = require('axios');
const fs = require('fs');
const path = require('path');

class APIClient {
  constructor(baseURL, logger) {
    this.baseURL = baseURL || process.env.API_BASE_URL || 'http://localhost:3000/api';
    this.logger = logger;
    this.saveResponses = process.env.SAVE_RESPONSES === 'true';
    
    // Create responses directory if saving is enabled
    if (this.saveResponses) {
      const responsesDir = path.join(__dirname, '..', 'results', 'responses');
      if (!fs.existsSync(responsesDir)) {
        fs.mkdirSync(responsesDir, { recursive: true });
      }
      this.responsesDir = responsesDir;
    }

    // Create axios instance with default config
    this.client = axios.create({
      baseURL: this.baseURL,
      timeout: parseInt(process.env.TEST_TIMEOUT) || 30000,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'ATMA-E2E-Test-Client/1.0.0'
      }
    });

    // Add request interceptor for logging
    this.client.interceptors.request.use(
      (config) => {
        this.logger.debug(`API Request: ${config.method.toUpperCase()} ${config.url}`, {
          headers: config.headers,
          data: config.data
        });
        return config;
      },
      (error) => {
        this.logger.error('API Request Error:', error);
        return Promise.reject(error);
      }
    );

    // Add response interceptor for logging and saving
    this.client.interceptors.response.use(
      (response) => {
        this.logger.debug(`API Response: ${response.status} ${response.config.method.toUpperCase()} ${response.config.url}`, {
          status: response.status,
          headers: response.headers,
          data: response.data
        });
        
        this._saveResponse(response);
        return response;
      },
      (error) => {
        if (error.response) {
          this.logger.error(`API Error Response: ${error.response.status} ${error.config.method.toUpperCase()} ${error.config.url}`, {
            status: error.response.status,
            headers: error.response.headers,
            data: error.response.data
          });
          this._saveResponse(error.response, true);
        } else {
          this.logger.error('API Network Error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  _saveResponse(response, isError = false) {
    if (!this.saveResponses) return;

    try {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const method = response.config.method.toUpperCase();
      const endpoint = response.config.url.replace(this.baseURL, '').replace(/\//g, '_');
      const status = response.status;
      
      const filename = `${timestamp}_${method}_${endpoint}_${status}${isError ? '_ERROR' : ''}.json`;
      const filepath = path.join(this.responsesDir, filename);
      
      const responseData = {
        timestamp: new Date().toISOString(),
        request: {
          method: response.config.method,
          url: response.config.url,
          headers: response.config.headers,
          data: response.config.data
        },
        response: {
          status: response.status,
          statusText: response.statusText,
          headers: response.headers,
          data: response.data
        },
        isError
      };
      
      fs.writeFileSync(filepath, JSON.stringify(responseData, null, 2));
    } catch (error) {
      this.logger.warn('Failed to save response:', error.message);
    }
  }

  setAuthToken(token) {
    if (token) {
      this.client.defaults.headers.common['Authorization'] = `Bearer ${token}`;
      this.logger.debug('Auth token set for API client');
    } else {
      delete this.client.defaults.headers.common['Authorization'];
      this.logger.debug('Auth token removed from API client');
    }
  }

  // Authentication endpoints
  async register(userData) {
    this.logger.info(`Registering user: ${userData.email}`);
    const response = await this.client.post('/auth/register', userData);
    return response.data;
  }

  async login(credentials) {
    this.logger.info(`Logging in user: ${credentials.email}`);
    const response = await this.client.post('/auth/login', credentials);
    return response.data;
  }

  async logout() {
    this.logger.info('Logging out user');
    const response = await this.client.post('/auth/logout');
    return response.data;
  }

  async getProfile() {
    this.logger.info('Getting user profile');
    const response = await this.client.get('/auth/profile');
    return response.data;
  }

  async updateProfile(profileData) {
    this.logger.info('Updating user profile');
    const response = await this.client.put('/auth/profile', profileData);
    return response.data;
  }

  async changePassword(passwordData) {
    this.logger.info('Changing user password');
    const response = await this.client.post('/auth/change-password', passwordData);
    return response.data;
  }

  async getTokenBalance() {
    this.logger.info('Getting token balance');
    const response = await this.client.get('/auth/token-balance');
    return response.data;
  }

  async getSchools(params = {}) {
    this.logger.info('Getting schools list');
    const response = await this.client.get('/auth/schools', { params });
    return response.data;
  }

  // Assessment endpoints
  async submitAssessment(assessmentData) {
    this.logger.info('Submitting assessment');
    const response = await this.client.post('/assessment/submit', assessmentData);
    return response.data;
  }

  async getAssessmentStatus(jobId) {
    this.logger.debug(`Getting assessment status: ${jobId}`);
    const response = await this.client.get(`/assessment/status/${jobId}`);
    return response.data;
  }

  // Archive endpoints
  async getResults(params = {}) {
    this.logger.info('Getting assessment results');
    const response = await this.client.get('/archive/results', { params });
    return response.data;
  }

  async getResult(resultId) {
    this.logger.info(`Getting assessment result: ${resultId}`);
    const response = await this.client.get(`/archive/results/${resultId}`);
    return response.data;
  }

  async deleteResult(resultId) {
    this.logger.info(`Deleting assessment result: ${resultId}`);
    const response = await this.client.delete(`/archive/results/${resultId}`);
    return response.data;
  }

  // Chatbot endpoints
  async createConversation(conversationData) {
    this.logger.info('Creating chatbot conversation');
    const response = await this.client.post('/chatbot/conversations', conversationData);
    return response.data;
  }

  async getConversations(params = {}) {
    this.logger.info('Getting chatbot conversations');
    const response = await this.client.get('/chatbot/conversations', { params });
    return response.data;
  }

  async getConversation(conversationId, params = {}) {
    this.logger.info(`Getting conversation: ${conversationId}`);
    const response = await this.client.get(`/chatbot/conversations/${conversationId}`, { params });
    return response.data;
  }

  async sendMessage(conversationId, messageData) {
    this.logger.info(`Sending message to conversation: ${conversationId}`);
    const response = await this.client.post(`/chatbot/conversations/${conversationId}/messages`, messageData);
    return response.data;
  }

  async getMessages(conversationId, params = {}) {
    this.logger.info(`Getting messages for conversation: ${conversationId}`);
    const response = await this.client.get(`/chatbot/conversations/${conversationId}/messages`, { params });
    return response.data;
  }

  async deleteConversation(conversationId) {
    this.logger.info(`Deleting conversation: ${conversationId}`);
    const response = await this.client.delete(`/chatbot/conversations/${conversationId}`);
    return response.data;
  }

  async createConversationFromAssessment(assessmentData) {
    this.logger.info('Creating conversation from assessment');
    const response = await this.client.post('/chatbot/conversations/from-assessment', assessmentData);
    return response.data;
  }

  // Health check endpoints
  async healthCheck() {
    this.logger.debug('Performing health check');
    const response = await this.client.get('/health');
    return response.data;
  }

  async notificationHealthCheck() {
    this.logger.debug('Performing notification health check');
    const response = await this.client.get('/notifications/health');
    return response.data;
  }
}

module.exports = APIClient;
