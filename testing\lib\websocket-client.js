const { io } = require('socket.io-client');
const EventEmitter = require('events');

class WebSocketClient extends EventEmitter {
  constructor(url, logger) {
    super();
    this.url = url || process.env.WEBSOCKET_URL || 'http://localhost:3000';
    this.logger = logger;
    this.socket = null;
    this.isAuthenticated = false;
    this.userId = null;
    this.authTimeout = parseInt(process.env.WEBSOCKET_TIMEOUT) || 10000;
    this.notifications = [];
    this.connectionState = 'disconnected'; // disconnected, connected, authenticated
  }

  connect(options = {}) {
    return new Promise((resolve, reject) => {
      this.logger.info(`Connecting to WebSocket: ${this.url}`);
      
      const socketOptions = {
        autoConnect: false,
        transports: ['websocket', 'polling'],
        reconnection: true,
        reconnectionAttempts: 5,
        reconnectionDelay: 1000,
        reconnectionDelayMax: 5000,
        timeout: 20000,
        forceNew: false,
        ...options
      };

      this.socket = io(this.url, socketOptions);
      
      // Connection events
      this.socket.on('connect', () => {
        this.logger.success('WebSocket connected');
        this.connectionState = 'connected';
        this.emit('connected');
        resolve();
      });

      this.socket.on('connect_error', (error) => {
        this.logger.error('WebSocket connection error:', error.message);
        this.connectionState = 'disconnected';
        this.emit('connect_error', error);
        reject(error);
      });

      this.socket.on('disconnect', (reason) => {
        this.logger.warn(`WebSocket disconnected: ${reason}`);
        this.connectionState = 'disconnected';
        this.isAuthenticated = false;
        this.emit('disconnected', reason);
      });

      // Authentication events
      this.socket.on('authenticated', (data) => {
        this.logger.success('WebSocket authenticated', data);
        this.isAuthenticated = true;
        this.userId = data.userId;
        this.connectionState = 'authenticated';
        this.emit('authenticated', data);
      });

      this.socket.on('auth_error', (data) => {
        this.logger.error('WebSocket authentication error:', data.message);
        this.isAuthenticated = false;
        this.connectionState = 'connected';
        this.emit('auth_error', data);
      });

      // Notification events
      this.socket.on('analysis-started', (data) => {
        this.logger.info('Analysis started notification received', data);
        this.notifications.push({ type: 'analysis-started', data, timestamp: new Date() });
        this.emit('analysis-started', data);
      });

      this.socket.on('analysis-complete', (data) => {
        this.logger.success('Analysis complete notification received', data);
        this.notifications.push({ type: 'analysis-complete', data, timestamp: new Date() });
        this.emit('analysis-complete', data);
      });

      this.socket.on('analysis-failed', (data) => {
        this.logger.error('Analysis failed notification received', data);
        this.notifications.push({ type: 'analysis-failed', data, timestamp: new Date() });
        this.emit('analysis-failed', data);
      });

      // Generic notification handler
      this.socket.on('notification', (data) => {
        this.logger.info('Generic notification received', data);
        this.notifications.push({ type: 'notification', data, timestamp: new Date() });
        this.emit('notification', data);
      });

      // Start connection
      this.socket.connect();
    });
  }

  authenticate(token) {
    return new Promise((resolve, reject) => {
      if (!this.socket || !this.socket.connected) {
        return reject(new Error('WebSocket not connected'));
      }

      this.logger.info('Authenticating WebSocket connection');

      // Set up timeout for authentication
      const authTimeoutId = setTimeout(() => {
        this.logger.error('WebSocket authentication timeout');
        reject(new Error('Authentication timeout'));
      }, this.authTimeout);

      // Listen for authentication response
      const onAuthenticated = (data) => {
        clearTimeout(authTimeoutId);
        this.socket.off('auth_error', onAuthError);
        resolve(data);
      };

      const onAuthError = (data) => {
        clearTimeout(authTimeoutId);
        this.socket.off('authenticated', onAuthenticated);
        reject(new Error(data.message || 'Authentication failed'));
      };

      this.socket.once('authenticated', onAuthenticated);
      this.socket.once('auth_error', onAuthError);

      // Send authentication request
      this.socket.emit('authenticate', { token });
    });
  }

  disconnect() {
    if (this.socket) {
      this.logger.info('Disconnecting WebSocket');
      this.socket.disconnect();
      this.socket = null;
      this.isAuthenticated = false;
      this.userId = null;
      this.connectionState = 'disconnected';
    }
  }

  waitForNotification(type, timeout = 300000) {
    return new Promise((resolve, reject) => {
      this.logger.info(`Waiting for notification: ${type} (timeout: ${timeout}ms)`);
      
      const timeoutId = setTimeout(() => {
        this.logger.error(`Timeout waiting for notification: ${type}`);
        reject(new Error(`Timeout waiting for ${type} notification`));
      }, timeout);

      const handler = (data) => {
        clearTimeout(timeoutId);
        this.logger.success(`Received expected notification: ${type}`);
        resolve(data);
      };

      this.once(type, handler);
    });
  }

  waitForAnyNotification(types = ['analysis-started', 'analysis-complete', 'analysis-failed'], timeout = 300000) {
    return new Promise((resolve, reject) => {
      this.logger.info(`Waiting for any notification: ${types.join(', ')} (timeout: ${timeout}ms)`);
      
      const timeoutId = setTimeout(() => {
        this.logger.error(`Timeout waiting for any notification: ${types.join(', ')}`);
        reject(new Error(`Timeout waiting for notifications: ${types.join(', ')}`));
      }, timeout);

      const handlers = types.map(type => {
        const handler = (data) => {
          clearTimeout(timeoutId);
          // Remove all handlers
          types.forEach(t => this.off(t, handlers[types.indexOf(t)]));
          this.logger.success(`Received notification: ${type}`);
          resolve({ type, data });
        };
        this.once(type, handler);
        return handler;
      });
    });
  }

  getNotifications(type = null) {
    if (type) {
      return this.notifications.filter(n => n.type === type);
    }
    return this.notifications;
  }

  clearNotifications() {
    this.notifications = [];
    this.logger.debug('Cleared notification history');
  }

  getConnectionState() {
    return {
      state: this.connectionState,
      isConnected: this.socket && this.socket.connected,
      isAuthenticated: this.isAuthenticated,
      userId: this.userId,
      notificationCount: this.notifications.length
    };
  }

  // Test helper methods
  async connectAndAuthenticate(token) {
    await this.connect();
    await this.authenticate(token);
    return this.getConnectionState();
  }

  async waitForAssessmentCompletion(timeout = 300000) {
    this.logger.info('Waiting for assessment completion...');
    
    try {
      // First wait for analysis-started
      await this.waitForNotification('analysis-started', 30000);
      
      // Then wait for completion or failure
      const result = await this.waitForAnyNotification(['analysis-complete', 'analysis-failed'], timeout);
      
      if (result.type === 'analysis-failed') {
        throw new Error(`Assessment failed: ${result.data.error || result.data.message}`);
      }
      
      return result.data;
    } catch (error) {
      this.logger.error('Error waiting for assessment completion:', error.message);
      throw error;
    }
  }

  // Validation helpers
  validateNotificationStructure(notification, type) {
    const requiredFields = {
      'analysis-started': ['jobId', 'status', 'message', 'timestamp'],
      'analysis-complete': ['jobId', 'resultId', 'status', 'message', 'timestamp'],
      'analysis-failed': ['jobId', 'error', 'message', 'timestamp']
    };

    const required = requiredFields[type];
    if (!required) return false;

    return required.every(field => notification.hasOwnProperty(field));
  }
}

module.exports = WebSocketClient;
